"""
城市关卡管理服务
处理城市列表、关卡进度、星级系统
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func

from app.models.hotspot_collection import UserHotspotCollection
from app.models.user import User
from app.services.config_loader import config_loader

logger = logging.getLogger(__name__)


class CityLevelService:
    """城市关卡管理服务"""
    
    def __init__(self):
        # 从配置文件加载城市关卡配置
        self._load_configs()
    
    def _load_configs(self):
        """从配置文件加载配置"""
        try:
            # 加载城市配置
            self.city_configs = config_loader.get_cities_list()
            
            # 加载关卡类型配置
            self.level_types = config_loader.get_level_types_config()
            
            # 加载系统规则配置
            self.unlock_rules = config_loader.get_unlock_rules()
            self.star_system = config_loader.get_star_system_config()
            self.reset_system = config_loader.get_reset_system_config()

            # 设置场景文件映射模板
            self.scene_file_templates = {
                "thief": "{city_id}/scene_level_thief.xml",
                "garbage": "{city_id}/scene_level_garbage.xml",
                "quiz": "{city_id}/scene_level_monumentQuiz.xml"
            }

            logger.info(f"配置加载成功: {len(self.city_configs)} 个城市, {len(self.level_types)} 种关卡类型")
            
        except Exception as e:
            logger.error(f"加载城市关卡配置失败: {e}")
            # 使用默认配置作为降级方案
            self._load_default_configs()
    
    def _load_default_configs(self):
        """加载默认配置（降级方案）"""
        logger.warning("使用默认城市关卡配置")
        
        self.city_configs = {
            "beijing": {
                "id": "beijing",
                "name": "北京",
                "name_en": "Beijing",
                "image": "/img/cities/beijing.jpg",
                "thumbnail": "/img/cities/beijing_thumb.jpg",
                "description": "古都北京，历史文化名城",
                "unlock_level": 1,
                "scenes": [
                    {
                        "scene_id": "scene_level_1",
                        "name": "天安门广场",
                        "levels": ["thief", "garbage", "quiz"]
                    }
                ]
            }
        }
        
        self.level_types = {
            "thief": {
                "name": "小偷关",
                "name_en": "Thief Hunt",
                "icon": "/img/page_icons/thieficon-2.png",
                "scene_file": "/beijing/scene_level_thief.xml",  # 默认使用北京场景
                "color": "#e67e22",
                "description": "抓捕小偷，维护秩序"
            },
            "garbage": {
                "name": "垃圾关",
                "name_en": "Clean Up",
                "icon": "/img/page_icons/rubbish-1.png",
                "scene_file": "/beijing/scene_level_garbage.xml",  # 默认使用北京场景
                "color": "#2ecc71",
                "description": "清理垃圾，保护环境"
            },
            "quiz": {
                "name": "古迹问答关",
                "name_en": "Monument Quiz",
                "icon": "/img/page_icons/questions_icon.png",
                "scene_file": "/beijing/scene_level_monumentQuiz.xml",  # 默认使用北京场景
                "color": "#e74c3c",
                "description": "探索古迹，学习历史"
            }
        }

        # 场景文件映射模板
        self.scene_file_templates = {
            "thief": "{city_id}/scene_level_thief.xml",
            "garbage": "{city_id}/scene_level_garbage.xml",
            "quiz": "{city_id}/scene_level_monumentQuiz.xml"
        }

        self.unlock_rules = {"level_based": True, "progressive": True}
        self.star_system = {"stars_per_city": 3, "completion_threshold": 1}
        self.reset_system = {"allow_level_reset": True, "preserve_artifacts": True}
    
    async def get_cities_with_progress(
        self,
        db: AsyncSession,
        user_id: int
    ) -> Dict[str, Any]:
        """
        获取所有城市及用户进度信息
        
        Returns:
            包含城市列表和进度的完整数据
        """
        try:
            logger.info(f"开始获取用户{user_id}的城市进度")
            cities_data = []
            
            # 先获取用户等级，避免重复查询
            user_level = await self._get_user_level(db, user_id)
            logger.info(f"用户等级: {user_level}")
            
            for city_id, city_config in self.city_configs.items():
                logger.info(f"处理城市: {city_id}")
                
                # 简化版本：先只返回基本信息，不查询详细进度
                city_data = {
                    **city_config,
                    "is_unlocked": city_config["unlock_level"] <= user_level,  # 简化解锁判断
                    "total_stars": 0,  # 暂时设为0
                    "earned_stars": 0,  # 暂时设为0
                    "completion_rate": 0,  # 暂时设为0
                    "progress": {
                        "total_stars": 0,
                        "earned_stars": 0,
                        "completion_rate": 0
                    }
                }
                
                cities_data.append(city_data)
                logger.info(f"城市{city_id}处理完成")
            
            logger.info(f"所有城市处理完成，共{len(cities_data)}个城市")
            
            return {
                "success": True,
                "cities": cities_data,
                "total_cities": len(cities_data),
                "user_level": user_level
            }
            
        except Exception as e:
            logger.error(f"获取城市进度失败: user_id={user_id}, error={e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {"success": False, "error": "获取城市数据失败"}
    
    async def get_city_detail_with_progress(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str
    ) -> Dict[str, Any]:
        """
        获取单个城市的详细进度信息
        
        Returns:
            城市详细数据，包括所有关卡的星级状态
        """
        try:
            if city_id not in self.city_configs:
                return {"success": False, "error": "城市不存在"}
            
            city_config = self.city_configs[city_id]
            
            # 获取城市所有场景的关卡进度
            scenes_data = []
            total_stars = 0
            earned_stars = 0
            
            for scene_config in city_config["scenes"]:
                scene_id = scene_config["scene_id"]
                scene_progress = await self._get_scene_level_progress(db, user_id, city_id, scene_id)
                
                # 构建关卡数据
                levels_data = []
                for level_type in scene_config["levels"]:
                    level_config = self.level_types[level_type]
                    level_progress = scene_progress.get(level_type, {})
                    
                    level_data = {
                        **level_config,
                        "type": level_type,
                        "is_completed": level_progress.get("is_completed", False),
                        "collected_count": level_progress.get("collected_count", 0),
                        "total_count": level_progress.get("total_count", 0),
                        "completion_rate": level_progress.get("completion_rate", 0),
                        "can_reset": level_progress.get("can_reset", False),
                        "last_played": level_progress.get("last_played"),
                        "has_star": level_progress.get("is_completed", False)
                    }
                    
                    levels_data.append(level_data)
                    total_stars += 1
                    if level_data["has_star"]:
                        earned_stars += 1
                
                scene_data = {
                    **scene_config,
                    "levels": levels_data,
                    "stars": len([level for level in levels_data if level["has_star"]]),
                    "total_stars": len(levels_data)
                }
                
                scenes_data.append(scene_data)
            
            return {
                "success": True,
                "city": {
                    **city_config,
                    "scenes": scenes_data,
                    "total_stars": total_stars,
                    "earned_stars": earned_stars,
                    "completion_rate": (earned_stars / total_stars * 100) if total_stars > 0 else 0,
                    "is_unlocked": await self._check_city_unlock(db, user_id, city_config["unlock_level"])
                }
            }
            
        except Exception as e:
            logger.error(f"获取城市详情失败: user_id={user_id}, city_id={city_id}, error={e}")
            return {"success": False, "error": "获取城市详情失败"}
    
    async def _get_city_progress(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str
    ) -> Dict[str, Any]:
        """获取城市总进度"""
        try:
            city_config = self.city_configs.get(city_id, {})
            total_stars = 0
            earned_stars = 0
            
            scenes = city_config.get("scenes", [])
            for scene_config in scenes:
                scene_id = scene_config.get("scene_id")
                scene_progress = await self._get_scene_level_progress(db, user_id, city_id, scene_id)

                for level_type in scene_config.get("levels", []):
                    total_stars += 1
                    if scene_progress.get(level_type, {}).get("is_completed", False):
                        earned_stars += 1
            
            return {
                "total_stars": total_stars,
                "earned_stars": earned_stars,
                "completion_rate": (earned_stars / total_stars * 100) if total_stars > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取城市进度失败: {e}")
            return {"total_stars": 0, "earned_stars": 0, "completion_rate": 0}
    
    async def _get_scene_level_progress(
        self,
        db: AsyncSession,
        user_id: int,
        city_id: str,
        scene_id: str
    ) -> Dict[str, Dict[str, Any]]:
        """获取场景中各关卡类型的进度"""
        try:
            # 使用现有的热点收集服务获取进度
            from app.services.hotspot_collection_service import hotspot_collection_service
            
            # 获取关卡完成状态 - 复用已有的API
            result = await hotspot_collection_service.get_level_completion_status(
                db=db,
                user_id=user_id,
                city_id=city_id,
                scene_id=scene_id
            )
            
            progress = {}
            if result.get("success"):
                level_stats = result.get("level_stats", {})
                
                for level_type, stats in level_stats.items():
                    progress[level_type] = {
                        "is_completed": stats.get("collected", 0) > 0,
                        "collected_count": stats.get("collected", 0),
                        "total_count": len(stats.get("hotspot_ids", [])),
                        "completion_rate": (stats.get("collected", 0) / len(stats.get("hotspot_ids", [])) * 100) if stats.get("hotspot_ids") else 0,
                        "can_reset": stats.get("collected", 0) > 0,
                        "last_played": None  # 需要从其他地方获取
                    }
            
            return progress
            
        except Exception as e:
            logger.error(f"获取场景关卡进度失败: {e}")
            return {}
    
    async def _check_city_unlock(
        self,
        db: AsyncSession,
        user_id: int,
        required_level: int
    ) -> bool:
        """检查城市是否解锁"""
        try:
            user_level = await self._get_user_level(db, user_id)
            return user_level >= required_level
        except Exception as e:
            logger.error(f"检查城市解锁状态失败: {e}")
            return True  # 默认解锁
    
    async def _get_user_level(
        self,
        db: AsyncSession,
        user_id: int
    ) -> int:
        """获取用户等级"""
        try:
            result = await db.execute(
                select(User.guardian_level).where(User.id == user_id)
            )
            user_level = result.scalar()
            return user_level or 1
        except Exception as e:
            logger.error(f"获取用户等级失败: {e}")
            return 1
    
    def get_level_config(self, level_type: str) -> Dict[str, Any]:
        """获取关卡类型配置"""
        return self.level_types.get(level_type, {})
    
    def get_city_config(self, city_id: str) -> Dict[str, Any]:
        """获取城市配置"""
        return self.city_configs.get(city_id, {})
    
    def reload_configs(self):
        """重新加载配置"""
        logger.info("重新加载城市关卡配置")
        config_loader.reload_config()
        self._load_configs()
    
    def get_all_cities_count(self) -> int:
        """获取总城市数量"""
        return len(self.city_configs)
    
    def get_all_level_types_count(self) -> int:
        """获取总关卡类型数量"""
        return len(self.level_types)
    
    def is_city_unlock_based_on_level(self) -> bool:
        """检查城市解锁是否基于等级"""
        return self.unlock_rules.get("level_based", True)
    
    def is_progressive_unlock(self) -> bool:
        """检查是否为渐进式解锁"""
        return self.unlock_rules.get("progressive", True)
    
    def get_minimum_completion_rate(self) -> float:
        """获取解锁下一城市所需的最小完成率"""
        return self.unlock_rules.get("minimum_completion", 0.5)

    def get_level_type_config_for_city(self, level_type: str, city_id: str) -> Optional[Dict[str, Any]]:
        """
        获取特定城市的关卡类型配置

        Args:
            level_type: 关卡类型 (thief, garbage, quiz)
            city_id: 城市ID

        Returns:
            关卡类型配置，包含正确的场景文件路径
        """
        base_config = self.level_types.get(level_type)
        if not base_config:
            return None

        # 复制基础配置
        config = base_config.copy()

        # 如果配置文件中已经包含了正确的场景文件路径，直接使用
        # 否则使用模板生成（向后兼容）
        if "scene_file" in base_config and base_config["scene_file"]:
            # 配置文件中已有场景文件路径，直接使用
            config["scene_file"] = base_config["scene_file"]
        elif level_type in self.scene_file_templates:
            # 使用模板生成（向后兼容）
            config["scene_file"] = self.scene_file_templates[level_type].format(city_id=city_id)

        return config

    def get_all_level_types_for_city(self, city_id: str) -> Dict[str, Any]:
        """
        获取特定城市的所有关卡类型配置

        Args:
            city_id: 城市ID

        Returns:
            所有关卡类型配置的字典
        """
        result = {}
        for level_type in self.level_types.keys():
            config = self.get_level_type_config_for_city(level_type, city_id)
            if config:
                result[level_type] = config

        return result


# 全局实例
city_level_service = CityLevelService()