/**
 * krpano管理类
 * 负责与krpano进行交互
 */
class KrpanoManager {
  constructor() {
    this.krpano = null
    this.ready = false
    this.callbacks = new Map()
    this.setupGlobalCallbacks()
  }

  /**
   * 初始化krpano
   * @param {string} target - 目标DOM元素ID
   * @param {string} xml - XML配置文件路径
   * @param {object} options - 其他选项
   */
  init(target, xml = '/tour.xml', options = {}) {
    return new Promise((resolve, reject) => {
      if (!window.embedpano) {
        reject(new Error('krpano.js未加载'))
        return
      }

      const defaultOptions = {
        xml,
        target,
        html5: 'auto',
        mobilescale: 1.0,
        passQueryParameters: 'startscene,startlookat',
        initvars: {
          design: 'flat'
        },
        onready: (krpano) => {
          this.krpano = krpano
          this.ready = true
          this.setupGlobalCallbacks()
          this.setupCrossOriginHandler()
          resolve(krpano)
        }
      }

      window.embedpano({ ...defaultOptions, ...options })
    })
  }

  /**
   * 设置全局回调函数
   */
  setupGlobalCallbacks() {
    // LOD系统回调函数
    window.onLODSystemReady = () => {
      // console.log('LOD系统已准备就绪')
      if (this.krpano) {
        this.krpano.call('setup_hotspot_lod_events')
      }
      this.trigger('lodSystemReady')
    }

    // 场景切换回调
    window.appVueOnNewPanoLoaded = () => {
      // console.log('新场景已加载')
      this.trigger('newPanoLoaded')
    }

    // 热点点击回调
    window.onThiefClicked = (hotspotName) => {
      // console.log('小偷热点被点击:', hotspotName)
      this.trigger('thiefClicked', hotspotName)
    }

    window.onGarbageClicked = (hotspotName) => {
      // console.log('垃圾热点被点击:', hotspotName)
      this.trigger('garbageClicked', hotspotName)
    }

    window.onMonumentClicked = (hotspotName) => {
      // console.log('纪念碑热点被点击:', hotspotName)
      this.trigger('monumentClicked', hotspotName)
    }

    // 场景切换函数
    window.executeSceneTransition = () => {
      // console.log('执行场景切换')
      this.trigger('sceneTransition')
    }

    // 进入下一关回调
    window.enterNextLevel = (gateName) => {
      // console.log('进入下一关:', gateName)
      this.trigger('enterNextLevel', gateName)
    }
  }

  /**
   * 设置跨域处理
   */
  setupCrossOriginHandler() {
    if (!this.krpano) return

    // 监听图片加载事件，设置crossOrigin
    this.krpano.call('addEventListener', 'image.load', (e) => {
      // console.log('图片加载事件:', e)
    })

    // 监听热点加载事件
    this.krpano.call('addEventListener', 'hotspot.load', (e) => {
      // console.log('热点加载事件:', e)
    })

    // 监听XML加载完成事件
    this.krpano.call('addEventListener', 'xml.load', () => {
      // console.log('XML加载完成')
      this.setImageCrossOrigin()
    })
  }

  /**
   * 为所有图片设置crossOrigin属性
   */
  setImageCrossOrigin() {
    if (!this.krpano) return

    // 获取所有热点
    const hotspots = this.krpano.get('hotspot')
    if (hotspots) {
      for (let i = 0; i < hotspots.count; i++) {
        const hotspot = hotspots.get(i)
        if (hotspot.url) {
          // 尝试设置crossOrigin
          try {
            const img = new Image()
            img.crossOrigin = 'anonymous'
            img.src = hotspot.url
          } catch (error) {
            //  console.warn('设置图片crossOrigin失败:', error)
          }
        }
      }
    }
  }

  /**
   * 注册事件监听
   * @param {string} event - 事件名称
   * @param {function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, [])
    }
    this.callbacks.get(event).push(callback)
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {function} callback - 回调函数（可选）
   */
  off(event, callback = null) {
    if (!this.callbacks.has(event)) return

    if (callback) {
      const callbacks = this.callbacks.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.callbacks.delete(event)
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {...any} args - 事件参数
   */
  trigger(event, ...args) {
    if (this.callbacks.has(event)) {
      this.callbacks.get(event).forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          // console.error(`事件回调执行失败 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 获取krpano实例
   */
  getKrpano() {
    return this.krpano
  }

  /**
   * 检查是否已准备就绪
   */
  isReady() {
    return this.ready && this.krpano !== null
  }

  /**
   * 调用krpano方法
   */
  call(method, ...args) {
    if (this.isReady()) {
      return this.krpano.call(method, ...args)
    } else {
      // console.warn('krpano未准备就绪')
      return null
    }
  }

  /**
   * 获取krpano变量
   */
  get(variable) {
    if (this.isReady()) {
      return this.krpano.get(variable)
    } else {
      // console.warn('krpano未准备就绪')
      return null
    }
  }

  /**
   * 设置krpano变量
   */
  set(variable, value) {
    if (this.isReady()) {
      return this.krpano.set(variable, value)
    } else {
      // console.warn('krpano未准备就绪')
      return null
    }
  }

  /**
   * 获取小偷热点数据
   */  
  getThiefHotspots() {
    if (!this.isReady()) {
      // console.warn('krpano未准备就绪，无法获取小偷热点')
      return []
    }

    const hotspots = []
    try {
      // 方法1: 直接从krpano获取所有热点并过滤小偷类型
      const allHotspots = this.krpano.get('hotspot')
      if (allHotspots && allHotspots.count > 0) {
        for (let i = 0; i < allHotspots.count; i++) {
          const hotspot = allHotspots.getItem(i)
          
          // 检查热点名称是否包含thief
          if (hotspot && hotspot.name && hotspot.name.includes('thief')) {
            hotspots.push({
              name: hotspot.name,
              ath: parseFloat(hotspot.ath) || 0,
              atv: parseFloat(hotspot.atv) || 0,
              scale: parseFloat(hotspot.scale) || 0.8,
              visible: hotspot.visible === true || hotspot.visible === 'true',
              alpha: parseFloat(hotspot.alpha) || 1,
              enabled: hotspot.enabled === true || hotspot.enabled === 'true'
            })
          }
        }
      }
      
      // 方法2: 如果上面没找到，尝试传统方式查找简单命名的热点
      if (hotspots.length === 0) {
        // console.log('未找到复杂命名的小偷热点，尝试查找简单命名热点...')
        
        for (let i = 1; i <= 100; i++) {
          const hotspotName = `thief_${i}`
          const hotspot = this.get(`hotspot[${hotspotName}]`)
          
          if (hotspot && hotspot.name) {
            hotspots.push({
              name: hotspotName,
              ath: parseFloat(hotspot.ath) || 0,
              atv: parseFloat(hotspot.atv) || 0,
              scale: parseFloat(hotspot.scale) || 0.8,
              visible: hotspot.visible === true,
              alpha: parseFloat(hotspot.alpha) || 1,
              enabled: hotspot.enabled === true
            })
          }
        }
        
        // 也检查特殊命名的小偷热点
        const specialThiefNames = ['thief_11', 'thief_111', 'thief_20', 'thief_32', 'thief_14', 'thief_55', 'thief_16', 'thief_7']
        for (const hotspotName of specialThiefNames) {
          const hotspot = this.get(`hotspot[${hotspotName}]`)
          
          if (hotspot && hotspot.name && !hotspots.some(h => h.name === hotspotName)) {
            hotspots.push({
              name: hotspotName,
              ath: parseFloat(hotspot.ath) || 0,
              atv: parseFloat(hotspot.atv) || 0,
              scale: parseFloat(hotspot.scale) || 0.8,
              visible: hotspot.visible === true,
              alpha: parseFloat(hotspot.alpha) || 1,
              enabled: hotspot.enabled === true
            })
          }
        }
      }
      
      // console.log(`🎯 获取到 ${hotspots.length} 个小偷热点`)
      return hotspots
    } catch (error) {
      // console.error('获取小偷热点失败:', error)
      return []
    }
  }

  /**
   * 获取垃圾热点数据
   */
  getGarbageHotspots() {
    if (!this.isReady()) {
      // console.warn('krpano未准备就绪，无法获取垃圾热点')
      return []
    }

    const hotspots = []
    try {
      // 方法1: 直接从krpano获取所有热点并过滤垃圾类型
      const allHotspots = this.krpano.get('hotspot')
      if (allHotspots && allHotspots.count > 0) {
        for (let i = 0; i < allHotspots.count; i++) {
          const hotspot = allHotspots.getItem(i)
          
          // 检查热点名称是否包含garbage或rubbish
          if (hotspot && hotspot.name && (hotspot.name.includes('garbage') || hotspot.name.includes('rubbish'))) {
            hotspots.push({
              name: hotspot.name,
              ath: parseFloat(hotspot.ath) || 0,
              atv: parseFloat(hotspot.atv) || 0,
              scale: parseFloat(hotspot.scale) || 0.03,
              visible: hotspot.visible === true || hotspot.visible === 'true',
              alpha: parseFloat(hotspot.alpha) || 1,
              enabled: hotspot.enabled === true || hotspot.enabled === 'true'
            })
          }
        }
      }
      
      // 方法2: 如果上面没找到，尝试传统方式查找简单命名的热点
      if (hotspots.length === 0) {
        // console.log('未找到复杂命名的垃圾热点，尝试查找简单命名热点...')
        
        for (let i = 1; i <= 50; i++) {
          const hotspotName = `garbage_${i}`
          const hotspot = this.get(`hotspot[${hotspotName}]`)
          
          if (hotspot && hotspot.name) {
            hotspots.push({
              name: hotspotName,
              ath: parseFloat(hotspot.ath) || 0,
              atv: parseFloat(hotspot.atv) || 0,
              scale: parseFloat(hotspot.scale) || 0.03,
              visible: hotspot.visible === true,
              alpha: parseFloat(hotspot.alpha) || 1,
              enabled: hotspot.enabled === true
            })
          }
        }
      }
      
      // console.log(`🗑️ 获取到 ${hotspots.length} 个垃圾热点`)
      return hotspots
    } catch (error) {
      // console.error('获取垃圾热点失败:', error)
      return []
    }
  }

  /**
   * 获取问答热点数据
   */
  getQuizHotspots() {
    if (!this.isReady()) {
      //    console.warn('krpano未准备就绪，无法获取问答热点')
      return []
    }

    const hotspots = []
    try {
      // 遍历所有问答/古迹热点
      for (let i = 1; i <= 20; i++) {
        const hotspotName = `monument_${i}`
        const hotspot = this.get(`hotspot[${hotspotName}]`)
        
        if (hotspot && hotspot.name) {
          hotspots.push({
            name: hotspotName,
            ath: parseFloat(hotspot.ath) || 0,
            atv: parseFloat(hotspot.atv) || 0,
            scale: parseFloat(hotspot.scale) || 0.5,
            visible: hotspot.visible === true,
            alpha: parseFloat(hotspot.alpha) || 1,
            enabled: hotspot.enabled === true
          })
        }
      }
      
      // console.log(`📚 获取到 ${hotspots.length} 个问答热点`)
      return hotspots
    } catch (error) {
      // console.error('获取问答热点失败:', error)
      return []
    }
  }

  /**
   * 获取所有热点数据
   */
  getAllHotspots() {
    if (!this.isReady()) {
      // console.warn('krpano未准备就绪，无法获取所有热点')
      return []
    }

    const hotspots = []
    try {
      // 方法1: 直接从krpano获取所有热点
      const allHotspots = this.krpano.get('hotspot')
      if (allHotspots && allHotspots.count > 0) {
        for (let i = 0; i < allHotspots.count; i++) {
          const hotspot = allHotspots.getItem(i)
          
          if (hotspot && hotspot.name) {
            // 从热点名称推断类型
            let type = 'other'
            if (hotspot.name.includes('thief')) {
              type = 'thief'
            } else if (hotspot.name.includes('garbage') || hotspot.name.includes('rubbish')) {
              type = 'garbage'
            } else if (hotspot.name.includes('treasure')) {
              type = 'treasure'
            } else if (hotspot.name.includes('monument') || hotspot.name.includes('quiz')) {
              type = 'quiz'
            } else if (hotspot.name.includes('boss')) {
              type = 'boss'
            }

            hotspots.push({
              name: hotspot.name,
              ath: parseFloat(hotspot.ath) || 0,
              atv: parseFloat(hotspot.atv) || 0,
              scale: parseFloat(hotspot.scale) || 0.5,
              visible: hotspot.visible === true || hotspot.visible === 'true',
              alpha: parseFloat(hotspot.alpha) || 1,
              enabled: hotspot.enabled === true || hotspot.enabled === 'true',
              type: type
            })
          }
        }
      }

      // 方法2: 如果上面没找到，尝试传统方式查找简单命名的热点
      if (hotspots.length === 0) {
        // console.log('未找到复杂命名的热点，尝试查找简单命名热点...')
        
        // 遍历所有可能的热点名称模式
        const hotspotPatterns = [
          { prefix: 'thief_', count: 100 },
          { prefix: 'garbage_', count: 50 },
          { prefix: 'treasure_', count: 10 },
          { prefix: 'monument_', count: 20 },
          { prefix: 'boss_', count: 5 }
        ]

        for (const pattern of hotspotPatterns) {
          for (let i = 1; i <= pattern.count; i++) {
            const hotspotName = `${pattern.prefix}${i}`
            const hotspot = this.get(`hotspot[${hotspotName}]`)
            
            if (hotspot && hotspot.name) {
              hotspots.push({
                name: hotspotName,
                ath: parseFloat(hotspot.ath) || 0,
                atv: parseFloat(hotspot.atv) || 0,
                scale: parseFloat(hotspot.scale) || 0.5,
                visible: hotspot.visible === true,
                alpha: parseFloat(hotspot.alpha) || 1,
                enabled: hotspot.enabled === true,
                type: pattern.prefix.replace('_', '')
              })
            }
          }
        }

        // 添加特殊命名的热点
        const specialHotspots = ['thief_11', 'thief_111', 'thief_20', 'thief_32', 'thief_14', 'thief_55', 'thief_16', 'thief_7']
        for (const hotspotName of specialHotspots) {
          const hotspot = this.get(`hotspot[${hotspotName}]`)
          
          if (hotspot && hotspot.name && !hotspots.some(h => h.name === hotspotName)) {
            hotspots.push({
              name: hotspotName,
              ath: parseFloat(hotspot.ath) || 0,
              atv: parseFloat(hotspot.atv) || 0,
              scale: parseFloat(hotspot.scale) || 0.8,
              visible: hotspot.visible === true,
              alpha: parseFloat(hotspot.alpha) || 1,
              enabled: hotspot.enabled === true,
              type: 'thief'
            })
          }
        }
      }
      
      //  console.log(`🎯 总计获取到 ${hotspots.length} 个热点`)
      return hotspots
    } catch (error) {
      // console.error('获取所有热点失败:', error)
      return []
    }
  }

  /**
   * 销毁krpano实例
   */
  destroy() {
    if (this.krpano) {
      this.krpano.call('removepano')
      this.krpano = null
      this.ready = false
    }
    // 清理事件监听
    this.callbacks.clear()
  }
}

// 创建单例实例
const krpanoManager = new KrpanoManager()

export default krpanoManager