<template>
  <div>

    <div class="right-toolbar">
      <!-- 雷达工具 看广告获取雷达-->
      <div 
        class="toolbar-item radar-tool" 
        :class="{ disabled: !canUseRadar }" 
        @click="useRadar"
      >
        <img src="/img/page_icons/radar-2.png" alt="radar" class="tool-icon">
        <div class="notification-badge">
          <!-- <img src="/img/page_icons/ad_icon.png" alt="广告" class="ad-icon"> -->
        </div>
        <div class="item-name">{{ t('game.tools.radar') }}</div>
      </div>
      
      <!-- 放大镜工具 -->
      <div 
        class="toolbar-item magnifier-tool" 
        :class="{ disabled: !canUseMagnifier }" 
        @click="useMagnifier"
      >
        <img src="/img/page_icons/zoom-1-icon.png" alt="magnifier" class="tool-icon">
          <div class="notification-badge"></div>
          <div class="item-name">{{ t('game.tools.magnifier') }}</div>
        </div>
        <!-- 抽奖功能 -->
        <div 
          class="toolbar-item lottery-tool" 
          @click="openLotteryWheel"
        >
          <img src="/img/page_icons/spin_icon.png" alt="lottery" class="tool-icon">
          <div class="notification-badge"></div>
            <div class="item-name">{{ t('lottery.lottery') }}</div>
        </div>
        
        <!-- 体力恢复 -->
        <!-- <div 
          class="toolbar-item stamina-restore" 
          :class="{ disabled: gameStore.stamina >= gameStore.maxStamina }" 
          @click="restoreStaminaByAd"
        >
          <img style="width: 70px; height: 70px;" src="https://hunyuan-prod-1258344703.cos.ap-guangzhou.myqcloud.com/%2Ftext2img/611fae0b-bf28-4e5f-825b-704a8746c879.jpg?q-sign-algorithm=sha1&q-ak=AKIDRl074nOsGdJ9zjMsCRWP3ShmgS3VtX4S&q-sign-time=1753154021%3B1763954021&q-key-time=1753154021%3B1763954021&q-header-list=host&q-url-param-list=&q-signature=434391524fefaf9b79fbb9fb9cff4755e1c9bbe4&imageMogr2/strip/format/jpg/size-limit/1.9m!/ignore-error/1" alt="stamina" class="tool-icon">
          <div class="notification-badge">
            <img v-if="gameStore.stamina < gameStore.maxStamina" src="/img/page_icons/ad_icon.png" alt="广告" class="ad-icon">
          </div>
          <div class="item-name">体力恢复</div>
        </div> -->
      </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useGameStore } from '@/stores/game'
import { useI18n } from 'vue-i18n'
import { showToast, showSuccessToast, showFailToast, showLoadingToast } from 'vant'

const gameStore = useGameStore()
const { t } = useI18n()

// 工具状态
const radarCooldown = ref(0)
const magnifierCooldown = ref(0)
const isUsingRadar = ref(false)
const isUsingMagnifier = ref(false)
// 抽奖变量已移除

const emit = defineEmits(['radar-scan-result', 'magnifier-result', 'open-lottery-wheel'])
// 计算属性
const canUseRadar = computed(() => {
  return radarCooldown.value === 0 && !isUsingRadar.value
})

const canUseMagnifier = computed(() => {
  return magnifierCooldown.value === 0 && !isUsingMagnifier.value
})

const openLotteryWheel = () => {
  emit('open-lottery-wheel')
}

// 使用雷达
const useRadar = async () => {
  if (!canUseRadar.value) {
    if (radarCooldown.value > 0) {
      showToast(`雷达冷却中：${radarCooldown.value}秒`)
    } else if (isUsingRadar.value) {
      showToast('雷达正在使用中...')
    }
    return
  }

  try {
    isUsingRadar.value = true
    showLoadingToast('雷达扫描中...')

    // 动态导入krpano管理器
    const { default: krpanoManager } = await import('@/utils/krpano')
    
    // 检查krpano是否准备就绪
    if (!krpanoManager.isReady()) {
      showFailToast('krpano未准备就绪，请稍后再试')
      return
    }
    
    // 获取当前视角
    const currentView = krpanoManager.getView()
    console.log('雷达扫描 - 当前视角:', currentView)
    
    if (!currentView || (currentView.hlookat === null && currentView.vlookat === null)) {
      showFailToast('无法获取当前视角，请确保krpano已加载完成')
      return
    }

    // 获取所有小偷热点（优先扫描小偷）
    const availableThieves = gameStore.thieves.filter(thief => thief.position)
    console.log('availableThieves', availableThieves)
    if (availableThieves.length === 0) {
      showToast('当前场景没有发现小偷目标')
      return
    }

    // 扫描60度范围内的小偷热点
    const scanRange = 60
    let targetsInRange = availableThieves.filter(thief => {
      const distance = krpanoManager.getAngularDistance(
        currentView.hlookat, currentView.vlookat,
        parseFloat(thief.ath), parseFloat(thief.atv)
      )
      return distance <= scanRange
    })
    console.log('targetsInRange', targetsInRange)

    // 如果60度内没有目标，找最近的小偷
    if (targetsInRange.length === 0) {
      let closestThief = null
      let minDistance = Infinity

      availableThieves.forEach(thief => {
        const distance = krpanoManager.getAngularDistance(
          currentView.hlookat, currentView.vlookat,
          parseFloat(thief.ath), parseFloat(thief.atv)
        )
        
        if (distance < minDistance) {
          minDistance = distance
          closestThief = thief
        }
      })

      if (closestThief) {
        targetsInRange = [closestThief]
        showToast(`范围外发现小偷，距离 ${Math.round(minDistance)}°`)
      }
    }

    if (targetsInRange.length > 0) {
      // 选择最近的目标
      const targetThief = targetsInRange.reduce((closest, current) => {
        const closestDistance = krpanoManager.getAngularDistance(
          currentView.hlookat, currentView.vlookat,
          parseFloat(closest.ath), parseFloat(closest.atv)
        )
        const currentDistance = krpanoManager.getAngularDistance(
          currentView.hlookat, currentView.vlookat,
          parseFloat(current.ath), parseFloat(current.atv)
        )
        return currentDistance < closestDistance ? current : closest
      })

      // 显示雷达波动画
      krpanoManager.showRadarWave(targetThief.ath, targetThief.atv)
      
      // 平滑转向目标
      krpanoManager.lookToTarget(targetThief.ath, targetThief.atv)
      
      // 设置冷却时间
      gameStore.watchAd('radar', 'crazygames')
      startCooldown('radar')

      // 显示扫描结果
      showSuccessToast(`雷达锁定小偷：${targetThief.name || '未知目标'}`)

      // 触发事件通知父组件
      emit('radar-scan-result', {
        hotspots: targetsInRange.length,
        scanRange: scanRange,
        target: targetThief
      })

    } else {
      showToast('雷达未发现有效目标')
    }

  } catch (error) {
    console.error('雷达扫描失败:', error)
    showFailToast('雷达扫描失败')
  } finally {
    isUsingRadar.value = false
  }
}

// 使用放大镜
const useMagnifier = async () => {
  if (!canUseMagnifier.value) {
    if (magnifierCooldown.value > 0) {
      showToast(`放大镜冷却中：${magnifierCooldown.value}秒`)
    } else if (isUsingMagnifier.value) {
      showToast('放大镜正在使用中...')
    }
    return
  }

  try {
    isUsingMagnifier.value = true
    showLoadingToast('放大镜搜索中...')

    // 调用krpano放大镜功能
    const { default: krpanoManager } = await import('@/utils/krpano')
    const result = krpanoManager.useMagnifier(60) // 60度搜索范围

    // 🚫 PRD合规性清理：移除弹药消耗，PRD中没有弹药概念

    // 设置冷却时间
    magnifierCooldown.value = 30
    startCooldown('magnifier')

    if (result.success) {
      showSuccessToast(result.message)

      // 触发事件通知父组件
      emit('magnifier-result', {
        foundItems: result.targets?.map(t => t.name) || [],
        searchRadius: 60.0,
        targets: result.targets
      })
    } else {
      showToast(result.message)
    }

  } catch (error) {
    console.error('放大镜使用失败:', error)
    showFailToast('放大镜使用失败')
  } finally {
    isUsingMagnifier.value = false
  }
}

// 冷却计时器
const startCooldown = (toolType) => {
  const cooldownRef = toolType === 'radar' ? radarCooldown : magnifierCooldown

  const timer = setInterval(() => {
    if (cooldownRef.value > 0) {
      cooldownRef.value--
    } else {
      clearInterval(timer)
    }
  }, 1000)
}

// 抽奖功能已移除，但保留奖励经验的逻辑供其他功能使用
const awardGuardianExperience = (amount) => {
  gameStore.addGuardianExp(amount)
  showSuccessToast(`获得 ${amount} 守护者经验值！`)
}

const awardStamina = (amount) => {
  gameStore.addStamina(amount)
  showSuccessToast(`恢复 ${amount} 体力值！`)
}

const awardCulturalArtifact = (count = 1) => {
  // 图鉴奖励转换为守护者经验值
  const expAmount = count * 15
  gameStore.addGuardianExp(expAmount)
  showSuccessToast(`获得文化图鉴，转换为 ${expAmount} 守护者经验值！`)
}

// 通过广告恢复体力
const restoreStaminaByAd = async () => {
  if (gameStore.stamina >= gameStore.maxStamina) {
    showToast('体力值已满，无需恢复')
    return
  }
  
  try {
    showLoadingToast('观看广告中...')
    
    // 模拟广告观看
    setTimeout(() => {
      gameStore.restoreStaminaByAd()
      showSuccessToast(`体力恢复！当前体力: ${gameStore.stamina}/${gameStore.maxStamina}`)
    }, 2000)
  } catch (error) {
    console.error('观看广告失败:', error)
    showFailToast('广告观看失败')
  }
}

</script>

<style lang="scss" scoped>
@import '@/styles/toolbar.scss';

.right-toolbar {
  @include toolbar-base;
  right: 10px;
}

.toolbar-item {
  @include toolbar-item;
  
  .notification-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 18px;
    background: url('/img/page_icons/ad_icon.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
}

.item-icon {
  font-size: 24px;
  color: #fff;
  z-index: 2;
}

.item-count {
  @include item-count;
  right: 0;
  left: auto;
  transform: none;
}

.cooldown-overlay {
  @include cooldown-overlay;
}

.radar-tool {
  .tool-icon {
    height: 70px;
  }
}

.magnifier-tool {
  .tool-icon {
    height: 70px;
  }
}

.lottery-tool {
  .tool-icon {
    height: 70px;
  }
}

.disabled {
  opacity: 0.5;
}
</style> 